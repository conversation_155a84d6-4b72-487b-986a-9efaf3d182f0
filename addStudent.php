<?php
require("oop_classes/Student.php");
require("oop_classes/SessionManager.php");
require("includes/layout.php");

SessionManager::protect();

$message = "";
$messageType = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $student = new Student();
    $result = $student->addStudent(
        [
            'name' => trim($_POST['name']),
            'age' => $_POST['age'],
            'email' => trim($_POST['email']),
            'course' => trim($_POST['course'])
        ]
        );

    if ($result) {
        $message = "Student added successfully!";
        $messageType = "success";
        header('Location: addStudent.php?success=1');
        exit;
    } else {
        $message = "Error: A student with this email already exists!";
        $messageType = "error";
    }
}

if (isset($_GET['success']) && $_GET['success'] == '1') {
    $message = "Student added successfully!";
    $messageType = "success";
}

ob_start();
?>


<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Add New Student</h1>
                <p class="mt-2 text-gray-600">Fill in the details to add a new student to the system</p>
            </div>
            <a href="dashboard.php" class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (!empty($message)): ?>
        <?php echo renderAlert($message, $messageType); ?>
    <?php endif; ?>

    <!-- Add Student Form -->
    <div class="bg-white rounded-xl card-shadow p-8">
        <form method="POST" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Name Field -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Full Name <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        name="name"
                        id="name"
                        required
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 placeholder-gray-400"
                        placeholder="Enter student's full name"
                        value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>"
                    >
                </div>

                <!-- Age Field -->
                <div>
                    <label for="age" class="block text-sm font-medium text-gray-700 mb-2">
                        Age <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="number"
                        name="age"
                        id="age"
                        min="16"
                        max="100"
                        required
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 placeholder-gray-400"
                        placeholder="Enter age"
                        value="<?php echo isset($_POST['age']) ? htmlspecialchars($_POST['age']) : ''; ?>"
                    >
                </div>
            </div>

            <!-- Email Field -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    Email Address <span class="text-red-500">*</span>
                </label>
                <input
                    type="email"
                    name="email"
                    id="email"
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 placeholder-gray-400"
                    placeholder="Enter email address"
                    value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                >
            </div>

            <!-- Course Field -->
            <div>
                <label for="course" class="block text-sm font-medium text-gray-700 mb-2">
                    Course <span class="text-red-500">*</span>
                </label>
                <input
                    type="text"
                    name="course"
                    id="course"
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 placeholder-gray-400"
                    placeholder="Enter course name"
                    value="<?php echo isset($_POST['course']) ? htmlspecialchars($_POST['course']) : ''; ?>"
                >
            </div>

            <!-- Submit Button -->
            <div class="flex items-center justify-end space-x-4 pt-6">
                <a href="dashboard.php" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition duration-200">
                    Cancel
                </a>
                <button
                    type="submit"
                    class="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform transition duration-200 hover:scale-105"
                >
                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Student
                </button>
            </div>
        </form>
    </div>
</div>

<?php
$content = ob_get_clean();
renderLayout("Add Student", $content);
?>