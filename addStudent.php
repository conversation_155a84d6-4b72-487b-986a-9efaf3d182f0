<?php

require("oop_classes/Student.php");
require("oop_classes/SessionManager.php");

SessionManager::protect();

$message = "";
$messageType = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $student = new Student();
    $result = $student->addStudent(
        [
            'name' => $_POST['name'],
            'age' => $_POST['age'],
            'email' => $_POST['email'],
            'course' => $_POST['course']
        ]
        );

    if ($result) {
        $message = "Student added successfully!";
        $messageType = "success";
        header('Location: addStudent.php?success=1');
        exit;
    } else {
        $message = "Error: A student with this email already exists!";
        $messageType = "error";
    }
}

if (isset($_GET['success']) && $_GET['success'] == '1') {
    $message = "Student added successfully!";
    $messageType = "success";
}

?>


<!DOCTYPE html>
<html>
<head>
    <title>Add Student</title>
    <style>
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>

<h2>Add Student</h2><hr>

<?php if (!empty($message)): ?>
    <div class="message <?php echo $messageType; ?>">
        <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<form method="POST">
    Name: <input type="text" name="name" required><br><br>
    Age: <input type="number" name="age" required><br><br>
    Email: <input type="email" name="email" required><br><br>
    Course: <input type="text" name="course" required><br><br><br>
    <button type="submit">Add</button>
</form>
<a href="dashboard.php">⬅ Back</a>

</body>
</html>