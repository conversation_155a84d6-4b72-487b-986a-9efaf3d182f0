/* Custom CSS for Student Management System */

/* Additional animations and effects */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Utility classes */
.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
    animation: slideIn 0.3s ease-out;
}

.animate-pulse-hover:hover {
    animation: pulse 0.3s ease-in-out;
}

/* Enhanced gradient backgrounds */
.gradient-bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-success {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
}

.gradient-bg-warning {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.gradient-bg-danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

/* Enhanced card shadows */
.card-shadow-sm {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.card-shadow {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.card-shadow-lg {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Hover effects */
.hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* Form enhancements */
.form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 placeholder-gray-400;
}

.form-input:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button enhancements */
.btn-primary {
    @apply px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform transition duration-200;
}

.btn-primary:hover {
    transform: translateY(-1px);
}

.btn-secondary {
    @apply px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition duration-200;
}

.btn-success {
    @apply px-6 py-3 bg-gradient-to-r from-green-500 to-blue-600 text-white font-medium rounded-lg hover:from-green-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform transition duration-200;
}

.btn-danger {
    @apply px-6 py-3 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition duration-200;
}

/* Table enhancements */
.table-row-hover {
    @apply hover:bg-gray-50 transition duration-150;
}

/* Loading spinner */
.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive utilities */
@media (max-width: 640px) {
    .mobile-stack {
        flex-direction: column;
    }
    
    .mobile-full {
        width: 100%;
    }
    
    .mobile-text-center {
        text-align: center;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        background-color: #1a202c;
        color: #e2e8f0;
    }
    
    .dark-mode .card-shadow {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-full-width {
        width: 100% !important;
    }
}
