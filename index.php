<?php

require("oop_classes/User.php");
require("oop_classes/SessionManager.php");

SessionManager::start();

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $user = new User($_POST['username'], $_POST['password']);
    if ($user->authenticate()) {
        SessionManager::login($_POST['username']);
        header('Location: dashboard.php');
        echo 'user found';
        exit();
    } else {
        $error = "Invalid username or password";
        echo $error;
    }
}



?>
<div style="display: flex; flex-direction: column; align-items: center;">
    <h2 style="padding: 10px; margin-top: 12%;">Login</h2>
    <hr>
    <form method="POST">
        <b>Username:</b> <input type="text" name="username" style="padding: 10px; font-size: 16px; border-radius: 50px; border: none; focus:outline-none; letter-spacing: 2px;" required><br><br>
        <b>Password:</b> <input type="password" name="password" required><br><br>
        <div style="text-align: center; margin-top: 40px;">
            <button type="submit" style="padding: 10px; font-size: 16px; border-radius: 50px;0%;">Login</button>
        </div>
        
    </form>
</div>
