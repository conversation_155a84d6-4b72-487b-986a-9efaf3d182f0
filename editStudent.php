<?php
require("oop_classes/Student.php");
require("oop_classes/SessionManager.php");
require("includes/layout.php");

SessionManager::protect();

$studentObj = new Student();
$student = $studentObj->getStudentById($_GET['id']);

$message = "";
$messageType = "";

// Check if student exists
if (!$student) {
    header("Location: dashboard.php");
    exit;
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $studentObj->updateStudent([
        'id' => $_POST['id'],
        'name' => trim($_POST['name']),
        'age' => $_POST['age'],
        'email' => trim($_POST['email']),
        'course' => trim($_POST['course'])
    ]);

    $message = "Student updated successfully!";
    $messageType = "success";

    // Refresh student data
    $student = $studentObj->getStudentById($_POST['id']);
}

ob_start();
?>

<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit Student</h1>
                <p class="mt-2 text-gray-600">Update student information: <span class="font-semibold text-blue-600"><?php echo htmlspecialchars($student['name']); ?></span></p>
            </div>
            <a href="dashboard.php" class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (!empty($message)): ?>
        <?php echo renderAlert($message, $messageType); ?>
    <?php endif; ?>

    <!-- Edit Student Form -->
    <div class="bg-white rounded-xl card-shadow p-8">
        <form method="POST" class="space-y-6">
            <input type="hidden" name="id" value="<?php echo htmlspecialchars($student['id']); ?>">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Name Field -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Full Name <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        name="name"
                        id="name"
                        required
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 placeholder-gray-400"
                        placeholder="Enter student's full name"
                        value="<?php echo htmlspecialchars($student['name']); ?>"
                    >
                </div>

                <!-- Age Field -->
                <div>
                    <label for="age" class="block text-sm font-medium text-gray-700 mb-2">
                        Age <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="number"
                        name="age"
                        id="age"
                        min="16"
                        max="100"
                        required
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 placeholder-gray-400"
                        placeholder="Enter age"
                        value="<?php echo htmlspecialchars($student['age']); ?>"
                    >
                </div>
            </div>

            <!-- Email Field -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    Email Address <span class="text-red-500">*</span>
                </label>
                <input
                    type="email"
                    name="email"
                    id="email"
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 placeholder-gray-400"
                    placeholder="Enter email address"
                    value="<?php echo htmlspecialchars($student['email']); ?>"
                >
            </div>

            <!-- Course Field -->
            <div>
                <label for="course" class="block text-sm font-medium text-gray-700 mb-2">
                    Course <span class="text-red-500">*</span>
                </label>
                <input
                    type="text"
                    name="course"
                    id="course"
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 placeholder-gray-400"
                    placeholder="Enter course name"
                    value="<?php echo htmlspecialchars($student['course']); ?>"
                >
            </div>

            <!-- Submit Button -->
            <div class="flex items-center justify-end space-x-4 pt-6">
                <a href="dashboard.php" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition duration-200">
                    Cancel
                </a>
                <button
                    type="submit"
                    class="px-6 py-3 bg-gradient-to-r from-green-500 to-blue-600 text-white font-medium rounded-lg hover:from-green-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform transition duration-200 hover:scale-105"
                >
                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Update Student
                </button>
            </div>
        </form>
    </div>
</div>

<?php
$content = ob_get_clean();
renderLayout("Edit Student", $content);
?>
