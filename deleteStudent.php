<?php
require("oop_classes/Student.php");
require("oop_classes/SessionManager.php");
require("includes/layout.php");

SessionManager::protect();

$studentObj = new Student();
$message = "";
$messageType = "";

// Handle deletion
if (isset($_POST['confirm_delete']) && isset($_POST['id'])) {
    $result = $studentObj->deleteStudent($_POST['id']);
    if ($result) {
        header("Location: dashboard.php?deleted=1");
        exit;
    } else {
        $message = "Error: Failed to delete student. Please try again.";
        $messageType = "error";
    }
}

// Get student data for confirmation
if (isset($_GET['id'])) {
    $student = $studentObj->getStudentById($_GET['id']);
    if (!$student) {
        header("Location: dashboard.php");
        exit;
    }
} else {
    header("Location: dashboard.php");
    exit;
}

ob_start();
?>

<div class="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Alert Messages -->
    <?php if (!empty($message)): ?>
        <?php echo renderAlert($message, $messageType); ?>
    <?php endif; ?>

    <!-- Header -->
    <div class="mb-8 text-center">
        <div class="mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </div>
        <h1 class="text-2xl font-bold text-gray-900">Delete Student</h1>
        <p class="mt-2 text-gray-600">This action cannot be undone</p>
    </div>

    <!-- Confirmation Card -->
    <div class="bg-white rounded-xl card-shadow p-8">
        <div class="text-center mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Are you sure you want to delete this student?</h3>
            <div class="bg-gray-50 rounded-lg p-4 mb-4">
                <div class="flex items-center justify-center mb-3">
                    <div class="h-12 w-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <span class="text-white font-medium text-lg"><?php echo strtoupper(substr($student['name'], 0, 1)); ?></span>
                    </div>
                </div>
                <div class="space-y-1">
                    <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($student['name']); ?></p>
                    <p class="text-sm text-gray-600"><?php echo htmlspecialchars($student['email']); ?></p>
                    <p class="text-sm text-gray-600">Age: <?php echo htmlspecialchars($student['age']); ?> | Course: <?php echo htmlspecialchars($student['course']); ?></p>
                </div>
            </div>
            <p class="text-sm text-red-600">This will permanently remove the student from the system.</p>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-center space-x-4">
            <a href="dashboard.php" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition duration-200">
                Cancel
            </a>
            <form method="POST" class="inline">
                <input type="hidden" name="id" value="<?php echo htmlspecialchars($student['id']); ?>">
                <button
                    type="submit"
                    name="confirm_delete"
                    class="px-6 py-3 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition duration-200"
                >
                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Delete Student
                </button>
            </form>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
renderLayout("Delete Student", $content);
?>